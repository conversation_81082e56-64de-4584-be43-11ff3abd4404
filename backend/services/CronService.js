const PerformanceService = require('./performanceService');

/**
 * Cron Service for automated performance score updates
 * This service handles periodic updates of performance scores
 */

class CronService {
  
  /**
   * Start the cron service
   * This should be called when the server starts
   */
  static start() {
    console.log('🕐 Starting Performance Score Cron Service...');
    
    // Update expired deadlines every hour
    this.updateExpiredDeadlinesInterval = setInterval(async () => {
      try {
        console.log('🔄 Running automated performance score update...');
        const updatedCount = await PerformanceService.updateExpiredDeadlines();
        if (updatedCount > 0) {
          console.log(`✅ Updated ${updatedCount} expired campaign deadlines`);
        }
      } catch (error) {
        console.error('❌ Error in automated performance update:', error);
      }
    }, 60 * 60 * 1000); // Every hour
    
    // Also run once on startup after a delay
    setTimeout(async () => {
      try {
        console.log('🔄 Running initial performance score update...');
        const updatedCount = await PerformanceService.updateExpiredDeadlines();
        console.log(`✅ Initial update: ${updatedCount} expired campaign deadlines updated`);
      } catch (error) {
        console.error('❌ Error in initial performance update:', error);
      }
    }, 30000); // 30 seconds after startup
    
    console.log('✅ Performance Score Cron Service started');
  }
  
  /**
   * Stop the cron service
   * This should be called when the server shuts down
   */
  static stop() {
    if (this.updateExpiredDeadlinesInterval) {
      clearInterval(this.updateExpiredDeadlinesInterval);
      console.log('🛑 Performance Score Cron Service stopped');
    }
  }
}

module.exports = CronService;
